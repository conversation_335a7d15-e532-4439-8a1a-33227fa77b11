import { supabase } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
	try {
		const bookingId = params.id;

		console.log("=== BOOKING API ===");
		console.log("Booking ID:", bookingId);

		// Fetch reservation with related data
		const { data: reservation, error } = await supabase
			.from("reservations")
			.select(
				`
        *,
        service:services (
          id,
          name,
          image_url,
          duration_minutes
        ),
        customer:customers (
          id,
          first_name,
          last_name,
          email,
          phone
        ),
        participants:reservation_participants (
          id,
          first_name,
          last_name,
          age,
          individual_price
        )
      `
			)
			.eq("id", bookingId)
			.single();

		console.log("Database query result:", {
			error: error ? error.message : null,
			reservation: !!reservation,
			reservationId: reservation?.id,
		});

		if (error) {
			console.log("Database error:", error);
			return NextResponse.json({ error: "Erreur de base de données" }, { status: 500 });
		}

		if (!reservation) {
			console.log("No reservation found with ID:", bookingId);
			return NextResponse.json({ error: "Réservation non trouvée" }, { status: 404 });
		}

		// Transform data for confirmation page
		const bookingData = {
			bookingNumber: reservation.qr_code,
			customerName: `${reservation.customer.first_name} ${reservation.customer.last_name}`,
			email: reservation.customer.email,
			phone: reservation.customer.phone,
			bookingDate: reservation.created_at,
			totalAmount: reservation.total_amount,
			qrCode: reservation.qr_code,
			items: [
				{
					service: reservation.service.name,
					date: new Date(reservation.start_time).toISOString().split("T")[0],
					time: new Date(reservation.start_time).toLocaleTimeString("fr-FR", {
						hour: "2-digit",
						minute: "2-digit",
					}),
					participants: reservation.participant_count,
					price: reservation.total_amount,
					image: reservation.service.image_url || "/placeholder.svg",
				},
			],
		};

		return NextResponse.json({
			success: true,
			data: bookingData,
		});
	} catch (error) {
		console.error("Error fetching booking:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}
