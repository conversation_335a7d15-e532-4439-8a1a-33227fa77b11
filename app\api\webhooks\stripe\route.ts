import { calculateRemainingAmount } from "@/lib/deposit-settings";
import { createNotification, createPaymentReceivedNotification } from "@/lib/notifications";
import { verifyWebhookSignature } from "@/lib/stripe";
import { supabase, supabaseAdmin } from "@/lib/supabase";
import { headers } from "next/headers";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const body = await request.text();
		const headersList = headers();
		const signature = headersList.get("stripe-signature");

		if (!signature) {
			return NextResponse.json({ error: "Missing stripe-signature header" }, { status: 400 });
		}

		// Verify webhook signature
		const verification = verifyWebhookSignature(body, signature);

		if (!verification.success) {
			console.error("Webhook signature verification failed:", verification.error);
			return NextResponse.json({ error: "Invalid signature" }, { status: 400 });
		}

		const event = verification.event!;
		console.log("Received Stripe webhook:", event.type);

		// Handle different event types
		switch (event.type) {
			case "payment_intent.succeeded":
				await handlePaymentSucceeded(event);
				break;

			case "payment_intent.payment_failed":
				await handlePaymentFailed(event);
				break;

			case "payment_intent.requires_action":
				await handlePaymentRequiresAction(event);
				break;

			case "payment_intent.canceled":
				await handlePaymentCanceled(event);
				break;

			default:
				console.log(`Unhandled event type: ${event.type}`);
		}

		return NextResponse.json({ received: true });
	} catch (error) {
		console.error("Webhook error:", error);
		return NextResponse.json({ error: "Webhook handler failed" }, { status: 500 });
	}
}

async function handlePaymentSucceeded(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Update payment record
		const { data: payment, error: paymentError } = await adminClient
			.from("payments")
			.update({
				status: "completed", // Map to our database constraint
				payment_date: new Date().toISOString(),
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId)
			.select("*, reservation:reservations!payments_reservation_id_fkey(*)")
			.single();

		if (paymentError || !payment) {
			console.error("Error updating payment record:", paymentError);
			return;
		}

		// Update reservation status and deposit information
		const reservationUpdate: any = {
			status: "confirmed",
			confirmed_at: new Date().toISOString(),
			updated_at: new Date().toISOString(),
		};

		// Handle deposit payments
		if (payment.is_deposit) {
			const remainingAmount = calculateRemainingAmount(payment.reservation.total_amount, payment.amount);
			reservationUpdate.deposit_amount = payment.amount;
			reservationUpdate.remaining_amount = remainingAmount;
			reservationUpdate.deposit_paid = true;
			reservationUpdate.deposit_payment_id = payment.id;
		}

		const { error: reservationError } = await adminClient
			.from("reservations")
			.update(reservationUpdate)
			.eq("id", payment.reservation_id);

		if (reservationError) {
			console.error("Error updating reservation status:", reservationError);
		}

		// Add reservation status history
		await adminClient.from("reservation_status_history").insert({
			reservation_id: payment.reservation_id,
			old_status: "pending",
			new_status: "confirmed",
			changed_by: null, // System change
			change_reason: "Payment confirmed via webhook",
		});

		// Create payment received notification for admins
		const { data: adminProfiles } = await supabase.from("profiles").select("id").eq("role", "admin");

		if (adminProfiles) {
			const notificationTemplate = createPaymentReceivedNotification(
				payment.amount,
				payment.currency,
				payment.reservation_id
			);

			for (const admin of adminProfiles) {
				await createNotification(admin.id, notificationTemplate, payment.reservation_id);
			}
		}

		console.log(`Payment succeeded for reservation ${payment.reservation_id}`);
	} catch (error) {
		console.error("Error handling payment succeeded:", error);
	}
}

async function handlePaymentFailed(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;
		const failureReason = paymentIntent.last_payment_error?.message || "Payment failed";

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Update payment record
		const { data: payment, error: paymentError } = await adminClient
			.from("payments")
			.update({
				status: "failed",
				failure_reason: failureReason,
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId)
			.select("*")
			.single();

		if (paymentError || !payment) {
			console.error("Error updating payment record:", paymentError);
			return;
		}

		// Check if there are other successful payments for this reservation
		const { data: successfulPayments } = await supabase
			.from("payments")
			.select("*")
			.eq("reservation_id", payment.reservation_id)
			.eq("status", "succeeded");

		// Only update reservation status if no successful payments exist
		if (!successfulPayments || successfulPayments.length === 0) {
			await supabase
				.from("reservations")
				.update({
					status: "payment_failed",
					updated_at: new Date().toISOString(),
				})
				.eq("id", payment.reservation_id);
		}

		console.log(`Payment failed for reservation ${payment.reservation_id}: ${failureReason}`);
	} catch (error) {
		console.error("Error handling payment failed:", error);
	}
}

async function handlePaymentRequiresAction(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;

		// Update payment record
		await supabase
			.from("payments")
			.update({
				status: "requires_action",
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId);

		console.log(`Payment requires action for payment intent ${paymentIntentId}`);
	} catch (error) {
		console.error("Error handling payment requires action:", error);
	}
}

async function handlePaymentCanceled(event: any) {
	try {
		const paymentIntent = event.data.object;
		const paymentIntentId = paymentIntent.id;

		// Update payment record
		const { data: payment, error: paymentError } = await supabase
			.from("payments")
			.update({
				status: "canceled",
				updated_at: new Date().toISOString(),
			})
			.eq("payment_intent_id", paymentIntentId)
			.select("*")
			.single();

		if (paymentError || !payment) {
			console.error("Error updating payment record:", paymentError);
			return;
		}

		// Check if there are other successful payments for this reservation
		const { data: successfulPayments } = await supabase
			.from("payments")
			.select("*")
			.eq("reservation_id", payment.reservation_id)
			.eq("status", "succeeded");

		// Only update reservation status if no successful payments exist
		if (!successfulPayments || successfulPayments.length === 0) {
			await supabase
				.from("reservations")
				.update({
					status: "canceled",
					updated_at: new Date().toISOString(),
				})
				.eq("id", payment.reservation_id);
		}

		console.log(`Payment canceled for reservation ${payment.reservation_id}`);
	} catch (error) {
		console.error("Error handling payment canceled:", error);
	}
}
