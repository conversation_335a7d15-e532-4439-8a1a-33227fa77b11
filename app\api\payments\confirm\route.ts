import { calculateRemainingAmount } from "@/lib/deposit-settings";
import { createNotification, createPaymentReceivedNotification } from "@/lib/notifications";
import { confirmPaymentIntent, isPaymentFailed, isPaymentSuccessful } from "@/lib/stripe";
import { supabase, supabaseAdmin } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
	try {
		const { paymentIntentId } = await request.json();

		console.log("=== PAYMENT CONFIRMATION API ===");
		console.log("Payment Intent ID:", paymentIntentId);

		if (!paymentIntentId) {
			console.log("ERROR: No payment intent ID provided");
			return NextResponse.json({ error: "Payment intent ID is required" }, { status: 400 });
		}

		// Confirm payment with Stripe
		console.log("Confirming payment with Stripe...");
		const result = await confirmPaymentIntent(paymentIntentId);

		console.log("Stripe confirmation result:", result);

		if (!result.success) {
			console.log("ERROR: Stripe confirmation failed:", result.error);
			return NextResponse.json({ error: result.error || "Failed to confirm payment" }, { status: 500 });
		}

		const { paymentIntent } = result;
		const paymentStatus = paymentIntent!.status;

		// Map Stripe status to our database status values
		const mapStripeStatusToDbStatus = (stripeStatus: string): string => {
			switch (stripeStatus) {
				case "succeeded":
					return "completed";
				case "processing":
					return "processing";
				case "requires_payment_method":
				case "requires_confirmation":
				case "requires_action":
				case "requires_capture":
					return "pending";
				case "canceled":
				case "payment_failed":
					return "failed";
				default:
					return "pending";
			}
		};

		// Use admin client for server-side operations
		const adminClient = supabaseAdmin || supabase;

		// Update payment record in database
		const updateData: any = {
			status: mapStripeStatusToDbStatus(paymentStatus),
			updated_at: new Date().toISOString(),
		};

		if (isPaymentSuccessful(paymentStatus)) {
			updateData.payment_date = new Date().toISOString();
		} else if (isPaymentFailed(paymentStatus)) {
			updateData.failure_reason = paymentIntent!.last_payment_error?.message || "Payment failed";
		}

		const { data: payment, error: paymentUpdateError } = await adminClient
			.from("payments")
			.update(updateData)
			.eq("payment_intent_id", paymentIntentId)
			.select("*, reservation:reservations(*)")
			.single();

		if (paymentUpdateError) {
			console.error("Error updating payment record:", paymentUpdateError);
			return NextResponse.json({ error: "Failed to update payment record" }, { status: 500 });
		}

		// If payment successful, update reservation status and deposit information
		if (isPaymentSuccessful(paymentStatus)) {
			const reservationUpdate: any = {
				status: "confirmed",
				confirmed_at: new Date().toISOString(),
				updated_at: new Date().toISOString(),
			};

			// Handle deposit payments
			if (payment.is_deposit) {
				const remainingAmount = calculateRemainingAmount(payment.reservation.total_amount, payment.amount);
				reservationUpdate.deposit_amount = payment.amount;
				reservationUpdate.remaining_amount = remainingAmount;
				reservationUpdate.deposit_paid = true;
				reservationUpdate.deposit_payment_id = payment.id;
			}

			const { error: reservationUpdateError } = await adminClient
				.from("reservations")
				.update(reservationUpdate)
				.eq("id", payment.reservation_id);

			if (reservationUpdateError) {
				console.error("Error updating reservation status:", reservationUpdateError);
				// Don't fail the request as payment was successful
			}

			// Create payment received notification for admins
			try {
				const { data: adminProfiles } = await supabase.from("profiles").select("id").eq("role", "admin");

				if (adminProfiles) {
					const notificationTemplate = createPaymentReceivedNotification(
						payment.amount,
						payment.currency,
						payment.reservation_id
					);

					for (const admin of adminProfiles) {
						await createNotification(admin.id, notificationTemplate, payment.reservation_id);
					}
				}
			} catch (notificationError) {
				console.error("Error creating payment notification:", notificationError);
				// Don't fail the request
			}

			// Add reservation status history
			try {
				await adminClient.from("reservation_status_history").insert({
					reservation_id: payment.reservation_id,
					old_status: "pending",
					new_status: "confirmed",
					changed_by: null, // System change
					change_reason: "Payment confirmed",
				});
			} catch (historyError) {
				console.error("Error adding status history:", historyError);
				// Don't fail the request
			}
		}

		return NextResponse.json({
			success: true,
			paymentStatus,
			paymentIntentId,
			reservationId: payment.reservation_id,
			amount: payment.amount,
			currency: payment.currency,
			isSuccessful: isPaymentSuccessful(paymentStatus),
			isFailed: isPaymentFailed(paymentStatus),
		});
	} catch (error) {
		console.error("Error confirming payment:", error);

		// Provide more specific error messages
		let errorMessage = "Internal server error";
		if (error instanceof Error) {
			errorMessage = error.message;
		}

		return NextResponse.json(
			{
				error: errorMessage,
				details: error instanceof Error ? error.message : "Unknown error occurred",
			},
			{ status: 500 }
		);
	}
}
