import { formatAmount } from "./stripe";
import { supabase } from "./supabase";

export interface PaymentReceiptData {
	paymentId: string;
	reservationId: string;
	reservationNumber: string;
	customerName: string;
	customerEmail: string;
	serviceName: string;
	serviceDate: string;
	serviceTime: string;
	participants: number;
	amount: number;
	currency: string;
	paymentDate: string;
	paymentMethod: string;
	paymentIntentId: string;
	// Deposit-related fields
	paymentType: PaymentType;
	depositPercentage?: number;
	totalAmount?: number;
	remainingAmount?: number;
	isDepositPayment: boolean;
}

export const generatePaymentReceipt = async (paymentIntentId: string): Promise<PaymentReceiptData | null> => {
	try {
		// Fetch payment and related data
		const { data: payment, error: paymentError } = await supabase
			.from("payments")
			.select(
				`
        *,
        reservation:reservations!payments_reservation_id_fkey(
          id,
          reservation_number,
          start_time,
          participant_count,
          total_amount,
          service:services(name),
          customer:customers(first_name, last_name, email)
        )
      `
			)
			.eq("payment_intent_id", paymentIntentId)
			.eq("status", "succeeded")
			.single();

		if (paymentError || !payment) {
			console.error("Payment not found or not successful:", paymentError);
			return null;
		}

		const reservation = payment.reservation;
		const customer = reservation.customer;
		const service = reservation.service;

		const isDepositPayment = payment.is_deposit || payment.payment_type === "deposit";
		const remainingAmount = isDepositPayment ? reservation.total_amount - payment.amount : 0;

		const receiptData: PaymentReceiptData = {
			paymentId: payment.id,
			reservationId: reservation.id,
			reservationNumber: reservation.reservation_number,
			customerName: `${customer.first_name} ${customer.last_name}`,
			customerEmail: customer.email,
			serviceName: service.name,
			serviceDate: new Date(reservation.start_time).toLocaleDateString("fr-FR"),
			serviceTime: new Date(reservation.start_time).toLocaleTimeString("fr-FR", {
				hour: "2-digit",
				minute: "2-digit",
			}),
			participants: reservation.participant_count,
			amount: payment.amount,
			currency: payment.currency,
			paymentDate: payment.payment_date || payment.updated_at,
			paymentMethod: payment.payment_method,
			paymentIntentId: payment.payment_intent_id,
			// Deposit-related fields
			paymentType: payment.payment_type || "full",
			depositPercentage: payment.deposit_percentage,
			totalAmount: reservation.total_amount,
			remainingAmount,
			isDepositPayment,
		};

		return receiptData;
	} catch (error) {
		console.error("Error generating payment receipt:", error);
		return null;
	}
};

export const generateReceiptHTML = (receiptData: PaymentReceiptData): string => {
	return `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Reçu de paiement - ${receiptData.reservationNumber}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          text-align: center;
          border-bottom: 2px solid #10b981;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .logo {
          font-size: 24px;
          font-weight: bold;
          color: #10b981;
          margin-bottom: 10px;
        }
        .receipt-title {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 20px;
        }
        .receipt-info {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 10px;
          padding: 5px 0;
          border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
          border-bottom: none;
        }
        .label {
          font-weight: bold;
          color: #6b7280;
        }
        .value {
          color: #111827;
        }
        .total-row {
          background-color: #10b981;
          color: white;
          padding: 15px;
          border-radius: 8px;
          margin: 20px 0;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          padding-top: 20px;
          border-top: 1px solid #e9ecef;
          color: #6b7280;
          font-size: 14px;
        }
        .success-badge {
          background-color: #10b981;
          color: white;
          padding: 5px 15px;
          border-radius: 20px;
          font-size: 14px;
          font-weight: bold;
          display: inline-block;
          margin-bottom: 20px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="logo">Soleil & Découverte</div>
        <div>Excursions éco-responsables en Guadeloupe</div>
      </div>

      <div class="receipt-title">Reçu de paiement</div>

      <div class="success-badge">✓ ${receiptData.isDepositPayment ? "Acompte confirmé" : "Paiement confirmé"}</div>

      <div class="receipt-info">
        <div class="info-row">
          <span class="label">Numéro de réservation:</span>
          <span class="value">${receiptData.reservationNumber}</span>
        </div>
        <div class="info-row">
          <span class="label">Client:</span>
          <span class="value">${receiptData.customerName}</span>
        </div>
        <div class="info-row">
          <span class="label">Email:</span>
          <span class="value">${receiptData.customerEmail}</span>
        </div>
        <div class="info-row">
          <span class="label">Service:</span>
          <span class="value">${receiptData.serviceName}</span>
        </div>
        <div class="info-row">
          <span class="label">Date de l'excursion:</span>
          <span class="value">${receiptData.serviceDate} à ${receiptData.serviceTime}</span>
        </div>
        <div class="info-row">
          <span class="label">Nombre de participants:</span>
          <span class="value">${receiptData.participants}</span>
        </div>
        <div class="info-row">
          <span class="label">Date de paiement:</span>
          <span class="value">${new Date(receiptData.paymentDate).toLocaleDateString("fr-FR")} à ${new Date(
		receiptData.paymentDate
	).toLocaleTimeString("fr-FR")}</span>
        </div>
        <div class="info-row">
          <span class="label">Méthode de paiement:</span>
          <span class="value">Carte bancaire (Stripe)</span>
        </div>
        <div class="info-row">
          <span class="label">Type de paiement:</span>
          <span class="value">${getPaymentTypeLabel(receiptData.paymentType)}</span>
        </div>
        <div class="info-row">
          <span class="label">ID de transaction:</span>
          <span class="value">${receiptData.paymentIntentId}</span>
        </div>
      </div>

      <div class="total-row">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <span style="font-size: 18px; font-weight: bold;">${
				receiptData.isDepositPayment ? "Acompte payé:" : "Montant payé:"
			}</span>
          <span style="font-size: 24px; font-weight: bold;">${formatAmount(receiptData.amount * 100)}</span>
        </div>
        ${
			receiptData.isDepositPayment
				? `
        <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid rgba(255,255,255,0.3);">
          <div style="display: flex; justify-content: space-between; font-size: 14px;">
            <span>Montant total de la réservation:</span>
            <span>${formatAmount(receiptData.totalAmount! * 100)}</span>
          </div>
          <div style="display: flex; justify-content: space-between; font-size: 14px; margin-top: 5px;">
            <span>Solde restant à payer:</span>
            <span>${formatAmount(receiptData.remainingAmount! * 100)}</span>
          </div>
        </div>
        `
				: ""
		}
      </div>

      <div class="footer">
        <p>Merci pour votre confiance !</p>
        <p>Ce reçu confirme que votre ${
			receiptData.isDepositPayment ? "acompte a été traité avec succès" : "paiement a été traité avec succès"
		}.</p>
        ${
			receiptData.isDepositPayment
				? "<p><strong>Important:</strong> Le solde restant devra être réglé avant votre excursion.</p>"
				: ""
		}
        <p>Pour toute question, contactez-nous à <EMAIL></p>
        <p style="margin-top: 20px; font-size: 12px;">
          Soleil & Découverte - Excursions éco-responsables<br>
          Guadeloupe, France
        </p>
      </div>
    </body>
    </html>
  `;
};

export const generateReceiptText = (receiptData: PaymentReceiptData): string => {
	return `
SOLEIL & DÉCOUVERTE
Excursions éco-responsables en Guadeloupe

REÇU DE PAIEMENT
================

✓ Paiement confirmé

Numéro de réservation: ${receiptData.reservationNumber}
Client: ${receiptData.customerName}
Email: ${receiptData.customerEmail}
Service: ${receiptData.serviceName}
Date de l'excursion: ${receiptData.serviceDate} à ${receiptData.serviceTime}
Nombre de participants: ${receiptData.participants}
Date de paiement: ${new Date(receiptData.paymentDate).toLocaleDateString("fr-FR")} à ${new Date(
		receiptData.paymentDate
	).toLocaleTimeString("fr-FR")}
Méthode de paiement: Carte bancaire (Stripe)
ID de transaction: ${receiptData.paymentIntentId}

MONTANT PAYÉ: ${formatAmount(receiptData.amount * 100)}

Merci pour votre confiance !
Ce reçu confirme que votre paiement a été traité avec succès.

Pour toute question, contactez-nous à <EMAIL>

Soleil & Découverte - Excursions éco-responsables
Guadeloupe, France
  `.trim();
};
